import { useState } from "react";
import { toast } from "sonner";
import { generateIntegrationToken } from "../../../services/api";
import { GenerateIntegrationTokenRequest } from "../../../services/types/payment";
import { DemoConfig } from "../types";
import { createGooglePayConfig } from "../../../utils/googlePayUtils";

export const useTokenGeneration = () => {
  const [embedUrl, setEmbedUrl] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const generateToken = async (config: DemoConfig) => {
    setLoading(true);
    setEmbedUrl("");

    try {
      const tokenRequest: GenerateIntegrationTokenRequest = {
        merchantId: config.merchantId,
        description: config.description,
        amount: config.amount,
        returnUrl: config.returnUrl,
        expiresIn: 60,
        // compliancePolicies removed - now retrieved from DynamoDB during token generation
      };

      if (config.enableGooglePay) {
        tokenRequest.enableDigitalWallets = true;
        tokenRequest.googlePayConfig = createGooglePayConfig(config.googlePayMerchantName || "Auth-Clear");
      }

      const response = await generateIntegrationToken(tokenRequest);

      if (response.success && response.data) {
        setEmbedUrl(response.data.embedUrl);
        toast.success("Integration token generated successfully!");
        return true;
      } else {
        throw new Error("Failed to generate token");
      }
    } catch {
      toast.error("Failed to generate integration token");
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    embedUrl,
    loading,
    generateToken,
  };
};
