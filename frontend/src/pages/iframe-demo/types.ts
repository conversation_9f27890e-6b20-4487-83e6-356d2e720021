// CompliancePolicies import removed - policies now managed during merchant onboarding

export interface DemoConfig {
  merchantId: string;
  description: string;
  amount: number;
  returnUrl: string;
  enableGooglePay?: boolean;
  googlePayEnvironment?: "TEST" | "PRODUCTION" | undefined;
  googlePayMerchantName?: string;
  // compliancePolicies removed - now retrieved from DynamoDB during token generation
}

export interface PaymentEvent {
  type: string;
  data: string | Record<string, unknown> | null;
  timestamp: string;
}
