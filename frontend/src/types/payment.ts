import type { GooglePayConfig } from "../services/types/payment";

export interface URLOnlyPolicy {
  type: "return_refund" | "delivery" | "privacy" | "security" | "terms";
  title: string;
  content?: string;
  url: string;
  lastUpdated?: string;
  version?: string;
}

export type MerchantPolicy = URLOnlyPolicy;

export interface CompliancePolicies {
  returnRefundPolicy: URLOnlyPolicy;
  deliveryPolicy?: URLOnlyPolicy;
  privacyPolicy: URLOnlyPolicy;
  securityPolicy: URLOnlyPolicy;
  termsAndConditions: URLOnlyPolicy;
}

export interface PayFieldsConfig {
  merchantId: string;
  publicKey: string;
  amount: number;
  description: string;
  mode: "txn" | "txnToken" | "token";
  txnType: "sale" | "auth" | "ecsale";
  googlePayConfig?: GooglePayConfig;
  enableDigitalWallets?: boolean;
}

export interface MerchantInfo {
  name: string;
  dba?: string;
  address?: {
    line1?: string;
    line2?: string;
    city?: string;
    state?: string;
    zip?: string;
  };
  contactEmail?: string;
  contactPhone?: string;
}

export interface PaymentInfo {
  description: string;
  amount: number;
  currency?: string;
  returnUrl?: string;
  items?: Array<{
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  taxAmount?: number;
  orderNumber?: string;
  compliancePolicies?: CompliancePolicies;
}

export interface BillingAddress {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
}

export interface PaymentIframeData {
  config: PayFieldsConfig;
  merchantInfo: MerchantInfo;
  paymentInfo: PaymentInfo;
}

export interface PaymentIframeState {
  payFieldsConfig: PayFieldsConfig | null;
  merchantInfo: MerchantInfo | null;
  paymentInfo: PaymentInfo | null;
  error: string | null;
  success: boolean;
  loading: boolean;
  billingAddress: BillingAddress;
  termsAccepted: boolean;
}
