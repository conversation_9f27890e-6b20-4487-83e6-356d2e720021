import { getGooglePayEnvironment } from "../utils/googlePayUtils";

export const DEFAULT_VALUES = {
  WEBSITE: "https://nowebsite.com",
  WEBSITE_PLACEHOLDER: "https://www.yourbusiness.com",
  WEBSITE_HINT: "Use https://nowebsite.com if you don't have one",
  RETURN_URL: "https://example.com/success",
  POLICY_URL_PLACEHOLDER: (label: string) => `https://yourwebsite.com/${label.toLowerCase().replace(/\s+/g, "-")}`,
  POLICY_URL_PLACEHOLDER_SIMPLE: "https://example.com/policy",
} as const;

export const DEMO_CONFIG = {
  MERCHANT_ID: "t1_mer_68c082d3cad7efa04eef2e5",
  DESCRIPTION: "Demo Product Purchase",
  AMOUNT_CENTS: 5000,
  RETURN_URL: "https://example.com/success",
  GOOGLE_PAY_ENABLED: true,
  GOOGLE_PAY_ENVIRONMENT: getGooglePayEnvironment(),
  GOOGLE_PAY_MERCHANT_NAME: "Auth-Clear Demo",
} as const;

export const LIMITS = {
  MAX_EVENTS: 10,
  DEFAULT_TIMEOUT_MS: 30000,
  RETRY_ATTEMPTS: 3,
} as const;

export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
} as const;

export const FORMAT_OPTIONS = {
  CURRENCY: {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
  },
  DATE: {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  },
} as const;
