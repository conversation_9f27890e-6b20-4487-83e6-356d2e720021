import { describe, it, expect, vi, beforeEach } from 'vitest';
import { 
  isProductionEnvironment, 
  createGooglePayConfig, 
  getGooglePayEnvironment,
  createPayFieldsGooglePayConfig 
} from '../googlePayUtils';

// Mock import.meta.env
const mockEnv = vi.hoisted(() => ({
  VITE_ENVIRONMENT: 'dev'
}));

vi.mock('import.meta', () => ({
  env: mockEnv
}));

describe('googlePayUtils', () => {
  beforeEach(() => {
    // Reset to dev environment before each test
    mockEnv.VITE_ENVIRONMENT = 'dev';
  });

  describe('isProductionEnvironment', () => {
    it('should return false for dev environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'dev';
      expect(isProductionEnvironment()).toBe(false);
    });

    it('should return false for staging environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'staging';
      expect(isProductionEnvironment()).toBe(false);
    });

    it('should return true for production environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'production';
      expect(isProductionEnvironment()).toBe(true);
    });
  });

  describe('getGooglePayEnvironment', () => {
    it('should return "TEST" for dev environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'dev';
      expect(getGooglePayEnvironment()).toBe('TEST');
    });

    it('should return "TEST" for staging environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'staging';
      expect(getGooglePayEnvironment()).toBe('TEST');
    });

    it('should return undefined for production environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'production';
      expect(getGooglePayEnvironment()).toBeUndefined();
    });
  });

  describe('createGooglePayConfig', () => {
    it('should include environment "TEST" for dev environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'dev';
      const config = createGooglePayConfig('Test Merchant');
      
      expect(config).toEqual({
        enabled: true,
        merchantName: 'Test Merchant',
        environment: 'TEST',
        allowedCardNetworks: ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'],
        allowedCardAuthMethods: ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
        billingAddressRequired: true,
        phoneNumberRequired: false,
      });
    });

    it('should omit environment for production environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'production';
      const config = createGooglePayConfig('Test Merchant');
      
      expect(config).toEqual({
        enabled: true,
        merchantName: 'Test Merchant',
        allowedCardNetworks: ['VISA', 'MASTERCARD', 'AMEX', 'DISCOVER'],
        allowedCardAuthMethods: ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
        billingAddressRequired: true,
        phoneNumberRequired: false,
      });
      
      expect(config).not.toHaveProperty('environment');
    });

    it('should allow custom options to override defaults', () => {
      mockEnv.VITE_ENVIRONMENT = 'dev';
      const config = createGooglePayConfig('Test Merchant', {
        billingAddressRequired: false,
        phoneNumberRequired: true,
      });
      
      expect(config.billingAddressRequired).toBe(false);
      expect(config.phoneNumberRequired).toBe(true);
    });
  });

  describe('createPayFieldsGooglePayConfig', () => {
    it('should include environment "TEST" for dev environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'dev';
      const config = createPayFieldsGooglePayConfig();
      
      expect(config).toEqual({
        enabled: true,
        environment: 'TEST',
      });
    });

    it('should omit environment for production environment', () => {
      mockEnv.VITE_ENVIRONMENT = 'production';
      const config = createPayFieldsGooglePayConfig();
      
      expect(config).toEqual({
        enabled: true,
      });
      
      expect(config).not.toHaveProperty('environment');
    });
  });
});
