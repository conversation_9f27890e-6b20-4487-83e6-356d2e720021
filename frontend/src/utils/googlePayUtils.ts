import type { GooglePayConfig } from "../services/types/payment";

/**
 * Determines if the current environment is production
 */
export const isProductionEnvironment = (): boolean => {
  return import.meta.env.VITE_ENVIRONMENT === "production";
};

/**
 * Creates Google Pay configuration based on the current environment
 * For production: omits environment parameter (per Payrix documentation)
 * For dev/staging: uses "TEST" environment
 */
export const createGooglePayConfig = (merchantName: string, options: Partial<GooglePayConfig> = {}): GooglePayConfig => {
  const baseConfig: GooglePayConfig = {
    enabled: true,
    merchantName,
    allowedCardNetworks: ["VISA", "MASTERCARD", "AMEX", "DISCOVER"],
    allowedCardAuthMethods: ["PAN_ONLY", "CRYPTOGRAM_3DS"],
    billingAddressRequired: true,
    phoneNumberRequired: false,
    ...options,
  };

  // For production environments, omit the environment parameter entirely
  // For dev/staging environments, include environment: "TEST"
  if (!isProductionEnvironment()) {
    baseConfig.environment = "TEST";
  }

  return baseConfig;
};

/**
 * Gets the appropriate Google Pay environment value for the current environment
 * Returns undefined for production (to omit the parameter)
 * Returns "TEST" for dev/staging
 */
export const getGooglePayEnvironment = (): "TEST" | undefined => {
  return isProductionEnvironment() ? undefined : "TEST";
};

/**
 * Creates a minimal Google Pay config for PayFields initialization
 */
export const createPayFieldsGooglePayConfig = (): { enabled: boolean; environment?: "TEST" | "PRODUCTION" } => {
  const config: { enabled: boolean; environment?: "TEST" | "PRODUCTION" } = {
    enabled: true,
  };

  // Only add environment for non-production environments
  if (!isProductionEnvironment()) {
    config.environment = "TEST";
  }

  return config;
};
